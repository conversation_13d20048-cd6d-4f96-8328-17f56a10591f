import { MigrationInterface, QueryRunner, Table, Index } from 'typeorm';

export class CreateProgressTables1756200013000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create lesson_content_progress table
        await queryRunner.createTable(
            new Table({
                name: 'lesson_content_progress',
                columns: [
                    {
                        name: 'id',
                        type: 'int',
                        isPrimary: true,
                        isGenerated: true,
                        generationStrategy: 'increment',
                    },
                    {
                        name: 'status',
                        type: 'enum',
                        enum: ['not_started', 'in_progress', 'completed'],
                        default: `'not_started'`,
                    },
                    {
                        name: 'score',
                        type: 'decimal',
                        precision: 5,
                        scale: 2,
                        isNullable: true,
                    },
                    {
                        name: 'timeSpentMinutes',
                        type: 'int',
                        default: 0,
                    },
                    {
                        name: 'startedAt',
                        type: 'timestamp with time zone',
                        isNullable: true,
                    },
                    {
                        name: 'completedAt',
                        type: 'timestamp with time zone',
                        isNullable: true,
                    },
                    {
                        name: 'metadata',
                        type: 'json',
                        isNullable: true,
                    },
                    {
                        name: 'notes',
                        type: 'text',
                        isNullable: true,
                    },
                    {
                        name: 'userId',
                        type: 'int',
                        isNullable: false,
                    },
                    {
                        name: 'lessonContentId',
                        type: 'int',
                        isNullable: false,
                    },
                    {
                        name: 'createdAt',
                        type: 'timestamp with time zone',
                        default: 'NOW()',
                    },
                    {
                        name: 'updatedAt',
                        type: 'timestamp with time zone',
                        default: 'NOW()',
                    },
                ],
                foreignKeys: [
                    {
                        columnNames: ['userId'],
                        referencedTableName: 'users',
                        referencedColumnNames: ['id'],
                        onDelete: 'CASCADE',
                    },
                    {
                        columnNames: ['lessonContentId'],
                        referencedTableName: 'lesson_contents',
                        referencedColumnNames: ['id'],
                        onDelete: 'CASCADE',
                    },
                ],
                indices: [
                    {
                        name: 'IDX_lesson_content_progress_user_content',
                        columnNames: ['userId', 'lessonContentId'],
                        isUnique: true,
                    },
                ],
            }),
            true,
        );

        // Create lesson_progress table
        await queryRunner.createTable(
            new Table({
                name: 'lesson_progress',
                columns: [
                    {
                        name: 'id',
                        type: 'int',
                        isPrimary: true,
                        isGenerated: true,
                        generationStrategy: 'increment',
                    },
                    {
                        name: 'status',
                        type: 'enum',
                        enum: ['not_started', 'in_progress', 'completed'],
                        default: `'not_started'`,
                    },
                    {
                        name: 'progressPercentage',
                        type: 'decimal',
                        precision: 5,
                        scale: 2,
                        default: 0.0,
                    },
                    {
                        name: 'averageScore',
                        type: 'decimal',
                        precision: 5,
                        scale: 2,
                        isNullable: true,
                    },
                    {
                        name: 'completedContents',
                        type: 'int',
                        default: 0,
                    },
                    {
                        name: 'totalContents',
                        type: 'int',
                        default: 0,
                    },
                    {
                        name: 'timeSpentMinutes',
                        type: 'int',
                        default: 0,
                    },
                    {
                        name: 'startedAt',
                        type: 'timestamp with time zone',
                        isNullable: true,
                    },
                    {
                        name: 'completedAt',
                        type: 'timestamp with time zone',
                        isNullable: true,
                    },
                    {
                        name: 'userId',
                        type: 'int',
                        isNullable: false,
                    },
                    {
                        name: 'lessonId',
                        type: 'int',
                        isNullable: false,
                    },
                    {
                        name: 'createdAt',
                        type: 'timestamp with time zone',
                        default: 'NOW()',
                    },
                    {
                        name: 'updatedAt',
                        type: 'timestamp with time zone',
                        default: 'NOW()',
                    },
                ],
                foreignKeys: [
                    {
                        columnNames: ['userId'],
                        referencedTableName: 'users',
                        referencedColumnNames: ['id'],
                        onDelete: 'CASCADE',
                    },
                    {
                        columnNames: ['lessonId'],
                        referencedTableName: 'lessons',
                        referencedColumnNames: ['id'],
                        onDelete: 'CASCADE',
                    },
                ],
                indices: [
                    {
                        name: 'IDX_lesson_progress_user_lesson',
                        columnNames: ['userId', 'lessonId'],
                        isUnique: true,
                    },
                ],
            }),
            true,
        );

        // Create course_progress table
        await queryRunner.createTable(
            new Table({
                name: 'course_progress',
                columns: [
                    {
                        name: 'id',
                        type: 'int',
                        isPrimary: true,
                        isGenerated: true,
                        generationStrategy: 'increment',
                    },
                    {
                        name: 'status',
                        type: 'enum',
                        enum: ['not_started', 'in_progress', 'completed'],
                        default: `'not_started'`,
                    },
                    {
                        name: 'progressPercentage',
                        type: 'decimal',
                        precision: 5,
                        scale: 2,
                        default: 0.0,
                    },
                    {
                        name: 'averageScore',
                        type: 'decimal',
                        precision: 5,
                        scale: 2,
                        isNullable: true,
                    },
                    {
                        name: 'completedLessons',
                        type: 'int',
                        default: 0,
                    },
                    {
                        name: 'totalLessons',
                        type: 'int',
                        default: 0,
                    },
                    {
                        name: 'timeSpentMinutes',
                        type: 'int',
                        default: 0,
                    },
                    {
                        name: 'startedAt',
                        type: 'timestamp with time zone',
                        isNullable: true,
                    },
                    {
                        name: 'completedAt',
                        type: 'timestamp with time zone',
                        isNullable: true,
                    },
                    {
                        name: 'userId',
                        type: 'int',
                        isNullable: false,
                    },
                    {
                        name: 'courseId',
                        type: 'int',
                        isNullable: false,
                    },
                    {
                        name: 'createdAt',
                        type: 'timestamp with time zone',
                        default: 'NOW()',
                    },
                    {
                        name: 'updatedAt',
                        type: 'timestamp with time zone',
                        default: 'NOW()',
                    },
                ],
                foreignKeys: [
                    {
                        columnNames: ['userId'],
                        referencedTableName: 'users',
                        referencedColumnNames: ['id'],
                        onDelete: 'CASCADE',
                    },
                    {
                        columnNames: ['courseId'],
                        referencedTableName: 'courses',
                        referencedColumnNames: ['id'],
                        onDelete: 'CASCADE',
                    },
                ],
                indices: [
                    {
                        name: 'IDX_course_progress_user_course',
                        columnNames: ['userId', 'courseId'],
                        isUnique: true,
                    },
                ],
            }),
            true,
        );

        // Create program_progress table
        await queryRunner.createTable(
            new Table({
                name: 'program_progress',
                columns: [
                    {
                        name: 'id',
                        type: 'int',
                        isPrimary: true,
                        isGenerated: true,
                        generationStrategy: 'increment',
                    },
                    {
                        name: 'status',
                        type: 'enum',
                        enum: ['not_started', 'in_progress', 'completed'],
                        default: `'not_started'`,
                    },
                    {
                        name: 'progressPercentage',
                        type: 'decimal',
                        precision: 5,
                        scale: 2,
                        default: 0.0,
                    },
                    {
                        name: 'averageScore',
                        type: 'decimal',
                        precision: 5,
                        scale: 2,
                        isNullable: true,
                    },
                    {
                        name: 'completedCourses',
                        type: 'int',
                        default: 0,
                    },
                    {
                        name: 'totalCourses',
                        type: 'int',
                        default: 0,
                    },
                    {
                        name: 'timeSpentMinutes',
                        type: 'int',
                        default: 0,
                    },
                    {
                        name: 'startedAt',
                        type: 'timestamp with time zone',
                        isNullable: true,
                    },
                    {
                        name: 'completedAt',
                        type: 'timestamp with time zone',
                        isNullable: true,
                    },
                    {
                        name: 'userId',
                        type: 'int',
                        isNullable: false,
                    },
                    {
                        name: 'programId',
                        type: 'int',
                        isNullable: false,
                    },
                    {
                        name: 'createdAt',
                        type: 'timestamp with time zone',
                        default: 'NOW()',
                    },
                    {
                        name: 'updatedAt',
                        type: 'timestamp with time zone',
                        default: 'NOW()',
                    },
                ],
                foreignKeys: [
                    {
                        columnNames: ['userId'],
                        referencedTableName: 'users',
                        referencedColumnNames: ['id'],
                        onDelete: 'CASCADE',
                    },
                    {
                        columnNames: ['programId'],
                        referencedTableName: 'programs',
                        referencedColumnNames: ['id'],
                        onDelete: 'CASCADE',
                    },
                ],
                indices: [
                    {
                        name: 'IDX_program_progress_user_program',
                        columnNames: ['userId', 'programId'],
                        isUnique: true,
                    },
                ],
            }),
            true,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable('program_progress');
        await queryRunner.dropTable('course_progress');
        await queryRunner.dropTable('lesson_progress');
        await queryRunner.dropTable('lesson_content_progress');
    }
}
