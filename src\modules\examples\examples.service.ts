import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProgressService } from '../progress/progress.service';
import { CertificatesService } from '../certificates/certificates.service';
import { CourseEnrollmentService } from '../course-enrollment/course-enrollment.service';
import { User } from '../users/entities/user.entity';
import { Course } from '../courses/entities/course.entity';
import { Program } from '../programs/entities/program.entity';
import { CourseProgress } from '../progress/entities/course-progress.entity';
import { ProgramProgress } from '../progress/entities/program-progress.entity';
import { LessonContentProgress } from '../progress/entities/lesson-content-progress.entity';

@Injectable()
export class ExamplesService {
  constructor(
    private readonly progressService: ProgressService,
    private readonly certificatesService: CertificatesService,
    private readonly courseEnrollmentService: CourseEnrollmentService,
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,
    @InjectRepository(Course)
    private readonly courseRepo: Repository<Course>,
    @InjectRepository(Program)
    private readonly programRepo: Repository<Program>,
    @InjectRepository(CourseProgress)
    private readonly courseProgressRepo: Repository<CourseProgress>,
    @InjectRepository(ProgramProgress)
    private readonly programProgressRepo: Repository<ProgramProgress>,
    @InjectRepository(LessonContentProgress)
    private readonly lessonContentProgressRepo: Repository<LessonContentProgress>,
  ) {}

  /**
   * Example 1: Get comprehensive user progress for a course
   * This includes overall course progress, lesson progress, and detailed content progress
   */
  async getUserCourseProgressDetailed(userId: number, courseId: number) {
    // Check enrollment first
    const isEnrolled = await this.courseEnrollmentService.isUserEnrolledInCourse(userId, courseId);
    if (!isEnrolled) {
      throw new Error('User is not enrolled in this course');
    }

    // Get course with all lessons and contents
    const course = await this.courseRepo.findOne({
      where: { id: courseId },
      relations: [
        'lessons',
        'lessons.contents',
        'lessons.contents.quiz',
        'lessons.contents.assignment',
        'lessons.contents.video',
        'instructor'
      ]
    });

    // Get overall course progress
    const courseProgress = await this.courseProgressRepo.findOne({
      where: { user: { id: userId }, course: { id: courseId } },
      relations: ['user', 'course']
    });

    // Get all lesson content progress for this course
    const contentProgresses = await this.lessonContentProgressRepo
      .createQueryBuilder('lcp')
      .leftJoinAndSelect('lcp.lessonContent', 'lc')
      .leftJoinAndSelect('lc.lesson', 'l')
      .leftJoinAndSelect('lcp.user', 'u')
      .where('l.courseId = :courseId', { courseId })
      .andWhere('u.id = :userId', { userId })
      .getMany();

    // Organize progress by lesson
    const lessonProgressMap = new Map();
    for (const lesson of course.lessons) {
      const lessonContents = contentProgresses.filter(
        cp => cp.lessonContent.lesson.id === lesson.id
      );
      
      lessonProgressMap.set(lesson.id, {
        lesson: {
          id: lesson.id,
          title: lesson.title,
          order: lesson.order,
        },
        contents: lessonContents.map(cp => ({
          id: cp.lessonContent.id,
          type: cp.lessonContent.type,
          status: cp.status,
          score: cp.score,
          timeSpent: cp.timeSpentMinutes,
          startedAt: cp.startedAt,
          completedAt: cp.completedAt,
          metadata: cp.metadata,
        })),
        totalContents: lesson.contents.length,
        completedContents: lessonContents.filter(cp => cp.status === 'completed').length,
      });
    }

    return {
      course: {
        id: course.id,
        title: course.title,
        instructor: course.instructor.name,
      },
      overallProgress: {
        status: courseProgress?.status || 'not_started',
        progressPercentage: courseProgress?.progressPercentage || 0,
        averageScore: courseProgress?.averageScore || 0,
        completedLessons: courseProgress?.completedLessons || 0,
        totalLessons: courseProgress?.totalLessons || course.lessons.length,
        timeSpentMinutes: courseProgress?.timeSpentMinutes || 0,
        startedAt: courseProgress?.startedAt,
        completedAt: courseProgress?.completedAt,
      },
      lessonProgress: Array.from(lessonProgressMap.values()),
    };
  }

  /**
   * Example 2: Get user's program progress with all courses
   */
  async getUserProgramProgressDetailed(userId: number, programId: number) {
    const program = await this.programRepo.findOne({
      where: { id: programId },
      relations: ['courses', 'courses.instructor']
    });

    const programProgress = await this.programProgressRepo.findOne({
      where: { user: { id: userId }, program: { id: programId } },
      relations: ['user', 'program']
    });

    const courseProgresses = await this.courseProgressRepo.find({
      where: { user: { id: userId }, course: { program: { id: programId } } },
      relations: ['course']
    });

    const courseProgressMap = new Map();
    courseProgresses.forEach(cp => {
      courseProgressMap.set(cp.course.id, cp);
    });

    return {
      program: {
        id: program.id,
        title: program.title,
        description: program.description,
      },
      overallProgress: {
        status: programProgress?.status || 'not_started',
        progressPercentage: programProgress?.progressPercentage || 0,
        averageScore: programProgress?.averageScore || 0,
        completedCourses: programProgress?.completedCourses || 0,
        totalCourses: programProgress?.totalCourses || program.courses.length,
        timeSpentMinutes: programProgress?.timeSpentMinutes || 0,
        startedAt: programProgress?.startedAt,
        completedAt: programProgress?.completedAt,
      },
      courseProgress: program.courses.map(course => {
        const progress = courseProgressMap.get(course.id);
        return {
          course: {
            id: course.id,
            title: course.title,
            instructor: course.instructor.name,
          },
          progress: {
            status: progress?.status || 'not_started',
            progressPercentage: progress?.progressPercentage || 0,
            averageScore: progress?.averageScore || 0,
            completedLessons: progress?.completedLessons || 0,
            totalLessons: progress?.totalLessons || 0,
            timeSpentMinutes: progress?.timeSpentMinutes || 0,
            startedAt: progress?.startedAt,
            completedAt: progress?.completedAt,
          }
        };
      }),
    };
  }

  /**
   * Example 3: Generate certificate when course is completed
   */
  async generateCertificateIfEligible(userId: number, courseId: number) {
    const courseProgress = await this.courseProgressRepo.findOne({
      where: { user: { id: userId }, course: { id: courseId } }
    });

    if (!courseProgress || courseProgress.status !== 'completed') {
      throw new Error('Course must be completed to generate certificate');
    }

    return await this.certificatesService.generateCourseCertificate(userId, courseId);
  }

  /**
   * Example 4: Get learning analytics for a user
   */
  async getUserLearningAnalytics(userId: number) {
    const user = await this.userRepo.findOne({ where: { id: userId } });
    
    // Get all course progresses
    const courseProgresses = await this.courseProgressRepo.find({
      where: { user: { id: userId } },
      relations: ['course', 'course.program']
    });

    // Get all program progresses
    const programProgresses = await this.programProgressRepo.find({
      where: { user: { id: userId } },
      relations: ['program']
    });

    // Get certificates
    const certificates = await this.certificatesService.getUserCertificates(userId);

    // Calculate statistics
    const totalCoursesEnrolled = courseProgresses.length;
    const completedCourses = courseProgresses.filter(cp => cp.status === 'completed').length;
    const inProgressCourses = courseProgresses.filter(cp => cp.status === 'in_progress').length;
    
    const totalProgramsEnrolled = programProgresses.length;
    const completedPrograms = programProgresses.filter(pp => pp.status === 'completed').length;
    
    const totalTimeSpent = courseProgresses.reduce((sum, cp) => sum + (cp.timeSpentMinutes || 0), 0);
    const averageScore = courseProgresses.length > 0 
      ? courseProgresses.reduce((sum, cp) => sum + (cp.averageScore || 0), 0) / courseProgresses.length 
      : 0;

    return {
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
      },
      statistics: {
        courses: {
          total: totalCoursesEnrolled,
          completed: completedCourses,
          inProgress: inProgressCourses,
          completionRate: totalCoursesEnrolled > 0 ? (completedCourses / totalCoursesEnrolled) * 100 : 0,
        },
        programs: {
          total: totalProgramsEnrolled,
          completed: completedPrograms,
          completionRate: totalProgramsEnrolled > 0 ? (completedPrograms / totalProgramsEnrolled) * 100 : 0,
        },
        performance: {
          averageScore: Math.round(averageScore * 100) / 100,
          totalTimeSpentHours: Math.round((totalTimeSpent / 60) * 100) / 100,
          certificatesEarned: certificates.length,
        }
      },
      recentActivity: {
        courses: courseProgresses
          .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
          .slice(0, 5)
          .map(cp => ({
            course: cp.course.title,
            status: cp.status,
            progress: cp.progressPercentage,
            lastActivity: cp.updatedAt,
          })),
        certificates: certificates.slice(0, 3).map(cert => ({
          title: cert.title,
          type: cert.type,
          score: cert.finalScore,
          issuedAt: cert.issuedAt,
        })),
      }
    };
  }

  /**
   * Example 5: Get instructor dashboard data
   */
  async getInstructorDashboard(instructorId: number) {
    const courses = await this.courseRepo.find({
      where: { instructor: { id: instructorId } },
      relations: ['lessons', 'enrollments', 'enrollments.user']
    });

    const dashboardData = [];

    for (const course of courses) {
      const enrollments = course.enrollments;
      const totalStudents = enrollments.length;
      
      // Get progress for all students in this course
      const courseProgresses = await this.courseProgressRepo.find({
        where: { course: { id: course.id } },
        relations: ['user']
      });

      const completedStudents = courseProgresses.filter(cp => cp.status === 'completed').length;
      const averageProgress = courseProgresses.length > 0 
        ? courseProgresses.reduce((sum, cp) => sum + cp.progressPercentage, 0) / courseProgresses.length 
        : 0;

      dashboardData.push({
        course: {
          id: course.id,
          title: course.title,
          totalLessons: course.lessons.length,
        },
        students: {
          total: totalStudents,
          completed: completedStudents,
          averageProgress: Math.round(averageProgress * 100) / 100,
        },
        recentEnrollments: enrollments
          .sort((a, b) => new Date(b.enrolledAt).getTime() - new Date(a.enrolledAt).getTime())
          .slice(0, 5)
          .map(enrollment => ({
            student: enrollment.user.name,
            enrolledAt: enrollment.enrolledAt,
            status: enrollment.status,
          })),
      });
    }

    return {
      instructor: {
        totalCourses: courses.length,
        totalStudents: courses.reduce((sum, course) => sum + course.enrollments.length, 0),
      },
      courses: dashboardData,
    };
  }
}
