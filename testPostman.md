# Postman API Testing Guide - Courses Platform

This comprehensive guide provides all API endpoints with JSON test data for the NestJS Courses Platform.

## Base URL
```
http://localhost:3000
```

## Environment Variables for Postman
Create these variables in your Postman environment:
- `baseUrl`: `http://localhost:3000`
- `studentToken`: (will be set after student login)
- `instructorToken`: (will be set after instructor login)
- `adminToken`: (will be set after admin login)

---

## 1. AUTHENTICATION ENDPOINTS

### 1.1 Register Student
**POST** `{{baseUrl}}/auth/register`

**Headers:**
```
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "password123",
  "phoneNumber": "+1234567890",
  "avatarUrl": "https://example.com/avatar.jpg",
  "role": "student"
}
```

### 1.2 Register Instructor
**POST** `{{baseUrl}}/auth/register`

**Body (JSON):**
```json
{
  "name": "<PERSON> Instructor",
  "email": "<EMAIL>",
  "password": "password123",
  "phoneNumber": "+1234567891",
  "avatarUrl": "https://example.com/instructor-avatar.jpg",
  "role": "instructor"
}
```

### 1.3 Register Admin
**POST** `{{baseUrl}}/auth/register`

**Body (JSON):**
```json
{
  "name": "Admin User",
  "email": "<EMAIL>",
  "password": "password123",
  "phoneNumber": "+1234567892",
  "avatarUrl": "https://example.com/admin-avatar.jpg",
  "role": "admin"
}
```

### 1.4 Login Student
**POST** `{{baseUrl}}/auth/login`

**Body (JSON):**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Test Script (save token):**
```javascript
if (pm.response.code === 200) {
    const response = pm.response.json();
    pm.environment.set("studentToken", response.accessToken);
}
```

### 1.5 Login Instructor
**POST** `{{baseUrl}}/auth/login`

**Body (JSON):**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Test Script:**
```javascript
if (pm.response.code === 200) {
    const response = pm.response.json();
    pm.environment.set("instructorToken", response.accessToken);
}
```

### 1.6 Login Admin
**POST** `{{baseUrl}}/auth/login`

**Body (JSON):**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Test Script:**
```javascript
if (pm.response.code === 200) {
    const response = pm.response.json();
    pm.environment.set("adminToken", response.accessToken);
}
```

### 1.7 Refresh Token
**POST** `{{baseUrl}}/auth/refresh`

**Body (JSON):**
```json
{
  "refreshToken": "your-refresh-token-here"
}
```

### 1.8 Get Profile
**GET** `{{baseUrl}}/auth/profile`

**Headers:**
```
Authorization: Bearer {{studentToken}}
```

---

## 2. PROGRAMS ENDPOINTS

### 2.1 Create Program (Admin Only)
**POST** `{{baseUrl}}/programs`

**Headers:**
```
Authorization: Bearer {{adminToken}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "title": "Computer Science Program",
  "description": "Comprehensive computer science program covering programming, algorithms, and software engineering.",
  "imgURL": "https://example.com/cs-program.jpg"
}
```

### 2.2 Get All Programs (Public)
**GET** `{{baseUrl}}/programs`

### 2.3 Get Program by ID (Public)
**GET** `{{baseUrl}}/programs/1`

### 2.4 Update Program (Admin Only)
**PATCH** `{{baseUrl}}/programs/1`

**Headers:**
```
Authorization: Bearer {{adminToken}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "title": "Advanced Computer Science Program",
  "description": "Updated comprehensive computer science program with advanced topics."
}
```

### 2.5 Delete Program (Admin Only)
**DELETE** `{{baseUrl}}/programs/1`

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

---

## 3. COURSES ENDPOINTS

### 3.1 Create Course (Instructor/Admin)
**POST** `{{baseUrl}}/courses`

**Headers:**
```
Authorization: Bearer {{instructorToken}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "title": "Introduction to JavaScript",
  "description": "Learn the fundamentals of JavaScript programming language.",
  "instructorId": 2,
  "programId": 1,
  "thumbnailURL": "https://example.com/js-course.jpg"
}
```

### 3.2 Get All Courses (Public)
**GET** `{{baseUrl}}/courses`

### 3.3 Get Course by ID (Public)
**GET** `{{baseUrl}}/courses/1`

### 3.4 Update Course (Instructor/Admin)
**PUT** `{{baseUrl}}/courses/1`

**Headers:**
```
Authorization: Bearer {{instructorToken}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "title": "Advanced JavaScript Programming",
  "description": "Deep dive into advanced JavaScript concepts and frameworks.",
  "thumbnailURL": "https://example.com/advanced-js.jpg"
}
```

### 3.5 Delete Course (Admin Only)
**DELETE** `{{baseUrl}}/courses/1`

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

---

## 4. LESSONS ENDPOINTS

### 4.1 Create Lesson (Instructor/Admin)
**POST** `{{baseUrl}}/lessons`

**Headers:**
```
Authorization: Bearer {{instructorToken}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "title": "Variables and Data Types",
  "description": "Learn about JavaScript variables, data types, and basic operations.",
  "order": 1,
  "coverImageURL": "https://example.com/lesson1-cover.jpg",
  "courseId": 1
}
```

### 4.2 Get All Lessons (Student/Instructor/Admin)
**GET** `{{baseUrl}}/lessons`

**Headers:**
```
Authorization: Bearer {{studentToken}}
```

### 4.3 Get Lesson by ID (Student/Instructor/Admin)
**GET** `{{baseUrl}}/lessons/1`

**Headers:**
```
Authorization: Bearer {{studentToken}}
```

### 4.4 Update Lesson (Instructor/Admin)
**PATCH** `{{baseUrl}}/lessons/1`

**Headers:**
```
Authorization: Bearer {{instructorToken}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "title": "Advanced Variables and Data Types",
  "description": "Updated lesson covering advanced variable concepts.",
  "order": 1
}
```

### 4.5 Delete Lesson (Admin Only)
**DELETE** `{{baseUrl}}/lessons/1`

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

---

## 5. LESSON CONTENTS ENDPOINTS

### 5.1 Create Text Content (Instructor/Admin)
**POST** `{{baseUrl}}/lesson-contents`

**Headers:**
```
Authorization: Bearer {{instructorToken}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "type": "text",
  "text": "JavaScript is a versatile programming language used for web development. Variables are containers for storing data values.",
  "lessonId": 1
}
```

### 5.2 Create Video Content (Instructor/Admin)
**POST** `{{baseUrl}}/lesson-contents`

**Body (JSON):**
```json
{
  "type": "video",
  "contentURL": "https://example.com/lesson-video.mp4",
  "lessonId": 1,
  "videoData": {
    "title": "JavaScript Variables Tutorial",
    "videoURL": "https://example.com/lesson-video.mp4",
    "description": "Comprehensive video tutorial on JavaScript variables",
    "duration": 1200,
    "thumbnailURL": "https://example.com/video-thumb.jpg"
  }
}
```

### 5.3 Create Quiz Content (Instructor/Admin)
**POST** `{{baseUrl}}/lesson-contents`

**Body (JSON):**
```json
{
  "type": "quiz",
  "lessonId": 1,
  "quizData": {
    "question": "Which keyword is used to declare a variable in JavaScript?",
    "options": ["var", "let", "const", "All of the above"],
    "correctAnswer": "All of the above"
  }
}
```

### 5.4 Create Assignment Content (Instructor/Admin)
**POST** `{{baseUrl}}/lesson-contents`

**Body (JSON):**
```json
{
  "type": "assignment",
  "contentURL": "https://example.com/assignment-instructions.pdf",
  "lessonId": 1,
  "assignmentData": {
    "title": "JavaScript Variables Practice",
    "description": "Complete the exercises on JavaScript variables and data types.",
    "fileURL": "https://example.com/assignment-template.zip",
    "dueDate": "2024-02-15T23:59:59.000Z"
  }
}
```

### 5.5 Create PDF Content (Instructor/Admin)
**POST** `{{baseUrl}}/lesson-contents`

**Body (JSON):**
```json
{
  "type": "pdf",
  "contentURL": "https://example.com/javascript-guide.pdf",
  "lessonId": 1
}
```

### 5.6 Get All Lesson Contents (Student/Instructor/Admin)
**GET** `{{baseUrl}}/lesson-contents`

**Headers:**
```
Authorization: Bearer {{studentToken}}
```

### 5.7 Get Lesson Content by ID (Student/Instructor/Admin)
**GET** `{{baseUrl}}/lesson-contents/1`

**Headers:**
```
Authorization: Bearer {{studentToken}}
```

### 5.8 Update Lesson Content (Instructor/Admin)
**PUT** `{{baseUrl}}/lesson-contents/1`

**Headers:**
```
Authorization: Bearer {{instructorToken}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "type": "text",
  "text": "Updated: JavaScript is a powerful programming language. Variables are fundamental building blocks."
}
```

### 5.9 Delete Lesson Content (Admin Only)
**DELETE** `{{baseUrl}}/lesson-contents/1`

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

---

## 6. QUIZZES ENDPOINTS

### 6.1 Create Quiz (Instructor/Admin)
**POST** `{{baseUrl}}/quizzes`

**Headers:**
```
Authorization: Bearer {{instructorToken}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "question": "What is the correct way to create a function in JavaScript?",
  "options": [
    "function myFunction() {}",
    "create myFunction() {}",
    "def myFunction() {}",
    "function = myFunction() {}"
  ],
  "correctAnswer": "function myFunction() {}"
}
```

### 6.2 Get All Quizzes (Student/Instructor/Admin)
**GET** `{{baseUrl}}/quizzes`

**Headers:**
```
Authorization: Bearer {{studentToken}}
```

### 6.3 Get Quiz by ID (Student/Instructor/Admin)
**GET** `{{baseUrl}}/quizzes/1`

**Headers:**
```
Authorization: Bearer {{studentToken}}
```

### 6.4 Update Quiz (Instructor/Admin)
**PUT** `{{baseUrl}}/quizzes/1`

**Headers:**
```
Authorization: Bearer {{instructorToken}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "question": "What is the correct way to create a function in JavaScript ES6?",
  "options": [
    "function myFunction() {}",
    "const myFunction = () => {}",
    "Both A and B",
    "None of the above"
  ],
  "correctAnswer": "Both A and B"
}
```

### 6.5 Delete Quiz (Admin Only)
**DELETE** `{{baseUrl}}/quizzes/1`

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

---

## 7. ASSIGNMENTS ENDPOINTS

### 7.1 Create Assignment (Instructor/Admin)
**POST** `{{baseUrl}}/assignments`

**Headers:**
```
Authorization: Bearer {{instructorToken}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "title": "JavaScript Functions Assignment",
  "description": "Create various JavaScript functions demonstrating different concepts learned in class.",
  "fileURL": "https://example.com/assignment-starter-code.zip",
  "dueDate": "2024-02-20T23:59:59.000Z",
  "lessonContentId": 1
}
```

### 7.2 Get All Assignments (Student/Instructor/Admin)
**GET** `{{baseUrl}}/assignments`

**Headers:**
```
Authorization: Bearer {{studentToken}}
```

### 7.3 Get Assignment by ID (Student/Instructor/Admin)
**GET** `{{baseUrl}}/assignments/1`

**Headers:**
```
Authorization: Bearer {{studentToken}}
```

### 7.4 Update Assignment (Instructor/Admin)
**PATCH** `{{baseUrl}}/assignments/1`

**Headers:**
```
Authorization: Bearer {{instructorToken}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "title": "Advanced JavaScript Functions Assignment",
  "description": "Updated assignment with additional requirements for arrow functions and closures.",
  "dueDate": "2024-02-25T23:59:59.000Z"
}
```

### 7.5 Delete Assignment (Admin Only)
**DELETE** `{{baseUrl}}/assignments/1`

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

---

## 8. VIDEOS ENDPOINTS

### 8.1 Create Video (Instructor/Admin)
**POST** `{{baseUrl}}/videos`

**Headers:**
```
Authorization: Bearer {{instructorToken}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "title": "JavaScript Arrays and Objects",
  "videoURL": "https://example.com/arrays-objects-tutorial.mp4",
  "description": "Comprehensive tutorial covering JavaScript arrays and objects with practical examples.",
  "duration": 1800,
  "thumbnailURL": "https://example.com/arrays-objects-thumb.jpg",
  "lessonContentId": 2
}
```

### 8.2 Get All Videos (Student/Instructor/Admin)
**GET** `{{baseUrl}}/videos`

**Headers:**
```
Authorization: Bearer {{studentToken}}
```

### 8.3 Get Video by ID (Student/Instructor/Admin)
**GET** `{{baseUrl}}/videos/1`

**Headers:**
```
Authorization: Bearer {{studentToken}}
```

### 8.4 Update Video (Instructor/Admin)
**PATCH** `{{baseUrl}}/videos/1`

**Headers:**
```
Authorization: Bearer {{instructorToken}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "title": "Advanced JavaScript Arrays and Objects",
  "description": "Updated tutorial with ES6+ features for arrays and objects.",
  "duration": 2100
}
```

### 8.5 Delete Video (Admin Only)
**DELETE** `{{baseUrl}}/videos/1`

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

---

## 9. USERS ENDPOINTS

### 9.1 Create User (Admin Only)
**POST** `{{baseUrl}}/users`

**Headers:**
```
Authorization: Bearer {{adminToken}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "name": "New Student User",
  "email": "<EMAIL>",
  "password": "password123",
  "phoneNumber": "+1234567893",
  "avatarUrl": "https://example.com/new-student-avatar.jpg"
}
```

### 9.2 Get All Users (Admin Only)
**GET** `{{baseUrl}}/users`

**Headers:**
```
Authorization: Bearer {{adminToken}}
```

### 9.3 Get User by ID (Own Profile or Admin)
**GET** `{{baseUrl}}/users/1`

**Headers:**
```
Authorization: Bearer {{studentToken}}
```

### 9.4 Update User (Own Profile or Admin)
**PATCH** `{{baseUrl}}/users/1`

**Headers:**
```
Authorization: Bearer {{studentToken}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "name": "John Updated Student",
  "phoneNumber": "+1234567899",
  "avatarUrl": "https://example.com/updated-avatar.jpg"
}
```

---

## 10. ERROR TESTING SCENARIOS

### 10.1 Test Unauthorized Access
**GET** `{{baseUrl}}/lessons`

**Headers:** (No Authorization header)

**Expected Response:** 401 Unauthorized

### 10.2 Test Forbidden Access (Student trying to create course)
**POST** `{{baseUrl}}/courses`

**Headers:**
```
Authorization: Bearer {{studentToken}}
Content-Type: application/json
```

**Body (JSON):**
```json
{
  "title": "Unauthorized Course",
  "description": "This should fail",
  "instructorId": 1
}
```

**Expected Response:** 403 Forbidden

### 10.3 Test Invalid Token
**GET** `{{baseUrl}}/lessons`

**Headers:**
```
Authorization: Bearer invalid-token-here
```

**Expected Response:** 401 Unauthorized

### 10.4 Test Validation Errors
**POST** `{{baseUrl}}/auth/register`

**Body (JSON):**
```json
{
  "name": "",
  "email": "invalid-email",
  "password": "123"
}
```

**Expected Response:** 400 Bad Request with validation errors

---

## 11. POSTMAN COLLECTION SETUP

### Environment Variables
Create a Postman environment with these variables:
```
baseUrl: http://localhost:3000
studentToken: (empty initially)
instructorToken: (empty initially)
adminToken: (empty initially)
```

### Collection Structure
1. **Authentication**
   - Register Student/Instructor/Admin
   - Login Student/Instructor/Admin
   - Refresh Token
   - Get Profile

2. **Programs** (CRUD)
   - Create, Read, Update, Delete

3. **Courses** (CRUD)
   - Create, Read, Update, Delete

4. **Lessons** (CRUD)
   - Create, Read, Update, Delete

5. **Lesson Contents** (CRUD)
   - Create (Text, Video, Quiz, Assignment, PDF)
   - Read, Update, Delete

6. **Quizzes** (CRUD)
   - Create, Read, Update, Delete

7. **Assignments** (CRUD)
   - Create, Read, Update, Delete

8. **Videos** (CRUD)
   - Create, Read, Update, Delete

9. **Users** (CRUD)
   - Create, Read, Update

10. **Error Testing**
    - Unauthorized, Forbidden, Invalid Token, Validation Errors

### Testing Order
1. First, register and login users to get tokens
2. Create programs (admin)
3. Create courses (instructor)
4. Create lessons (instructor)
5. Create lesson contents (instructor)
6. Test all CRUD operations
7. Test authorization scenarios
8. Test error scenarios

This comprehensive guide covers all API endpoints with proper JSON test data and authorization headers for complete testing of the Courses Platform API.
