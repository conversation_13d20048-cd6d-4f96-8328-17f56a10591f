import { Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';

@Injectable()
export class MailerService {
  private transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: 'smtp.gmail.com', // لو Gmail
      port: 587,
      secure: false,
      auth: {
        user: process.env.MAIL_USER, // إيميلك
        pass: process.env.MAIL_PASS, // App Password من Gmail
      },
    });
  }

  async sendMail(to: string, subject: string, text: string) {
    await this.transporter.sendMail({
      from: `"Course Platform" <${process.env.MAIL_USER}>`,
      to,
      subject,
      text,
    });
  }
}
