import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateAssignmentSubmissionsTable1756200015000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(
            new Table({
                name: 'assignment_submissions',
                columns: [
                    {
                        name: 'id',
                        type: 'int',
                        isPrimary: true,
                        isGenerated: true,
                        generationStrategy: 'increment',
                    },
                    {
                        name: 'submissionText',
                        type: 'text',
                        isNullable: true,
                    },
                    {
                        name: 'submissionFileUrl',
                        type: 'varchar',
                        length: '500',
                        isNullable: true,
                    },
                    {
                        name: 'status',
                        type: 'enum',
                        enum: ['submitted', 'graded', 'returned'],
                        default: `'submitted'`,
                    },
                    {
                        name: 'score',
                        type: 'decimal',
                        precision: 5,
                        scale: 2,
                        isNullable: true,
                    },
                    {
                        name: 'feedback',
                        type: 'text',
                        isNullable: true,
                    },
                    {
                        name: 'timeSpentMinutes',
                        type: 'int',
                        default: 0,
                    },
                    {
                        name: 'submittedAt',
                        type: 'timestamp with time zone',
                        isNullable: false,
                    },
                    {
                        name: 'gradedAt',
                        type: 'timestamp with time zone',
                        isNullable: true,
                    },
                    {
                        name: 'studentId',
                        type: 'int',
                        isNullable: false,
                    },
                    {
                        name: 'assignmentId',
                        type: 'int',
                        isNullable: false,
                    },
                    {
                        name: 'gradedById',
                        type: 'int',
                        isNullable: true,
                    },
                    {
                        name: 'createdAt',
                        type: 'timestamp with time zone',
                        default: 'NOW()',
                    },
                    {
                        name: 'updatedAt',
                        type: 'timestamp with time zone',
                        default: 'NOW()',
                    },
                ],
                foreignKeys: [
                    {
                        columnNames: ['studentId'],
                        referencedTableName: 'users',
                        referencedColumnNames: ['id'],
                        onDelete: 'CASCADE',
                    },
                    {
                        columnNames: ['assignmentId'],
                        referencedTableName: 'assignments',
                        referencedColumnNames: ['id'],
                        onDelete: 'CASCADE',
                    },
                    {
                        columnNames: ['gradedById'],
                        referencedTableName: 'users',
                        referencedColumnNames: ['id'],
                        onDelete: 'SET NULL',
                    },
                ],
                indices: [
                    {
                        name: 'IDX_assignment_submissions_student_assignment',
                        columnNames: ['studentId', 'assignmentId'],
                        isUnique: true,
                    },
                ],
            }),
            true,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable('assignment_submissions');
    }
}
