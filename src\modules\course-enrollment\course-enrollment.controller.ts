import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
} from '@nestjs/common';
import { CourseEnrollmentService } from './course-enrollment.service';
import { CreateCourseEnrollmentDto } from './dto/create-course-enrollment.dto';
import { UpdateCourseEnrollmentDto } from './dto/update-course-enrollment.dto';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';
import { Public } from '../auth/decorators/public.decorator';

@Controller('course-enrollments')
export class CourseEnrollmentController {
  constructor(
    private readonly courseEnrollmentService: CourseEnrollmentService,
  ) {}

  /** Students can enroll in a course */
  @Post()
  @Roles(UserRole.STUDENT)
  create(@Body() dto: CreateCourseEnrollmentDto) {
    return this.courseEnrollmentService.create(dto);
  }

  /** Admins only can see all enrollments */
  @Get()
  @Roles(UserRole.ADMIN)
  findAll() {
    return this.courseEnrollmentService.findAll();
  }

  /** A student can see his enrollment (or admin can see anyone’s) */
  @Get(':id')
  @Roles(UserRole.STUDENT, UserRole.ADMIN)
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.courseEnrollmentService.findOne(id);
  }

  /** Students can update their progress, Admins can also update */
  @Patch(':id')
  @Roles(UserRole.STUDENT, UserRole.ADMIN)
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateCourseEnrollmentDto,
  ) {
    return this.courseEnrollmentService.update(id, dto);
  }

  /** Only Admin can delete an enrollment (e.g. violation case) */
  @Delete(':id')
  @Roles(UserRole.ADMIN)
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.courseEnrollmentService.remove(id);
  }
}
