import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Course } from '../../courses/entities/course.entity';
import { EnrollmentStatus } from '../../common/enums/progress-status.enum';

@Entity('course_enrollments')
@Unique('IDX_course_enrollments_user_course', ['user', 'course'])
export class CourseEnrollment {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'enum',
    enum: EnrollmentStatus,
    default: EnrollmentStatus.ACTIVE,
  })
  status: EnrollmentStatus;

  @Column({ type: 'timestamptz' })
  enrolledAt: Date;

  @Column({
    type: 'decimal',
    precision: 5,
    scale: 2,
    default: 0.0,
  })
  progressPercentage: number;

  @Column({ type: 'int', default: 0 })
  completedLessons: number;

  @Column({ type: 'int', default: 0 })
  totalLessons: number;

  @Column({ type: 'text', nullable: true })
  notes?: string;

  /** RELATIONS **/
  @ManyToOne(() => User, (user) => user.courseEnrollments, { onDelete: 'CASCADE' })
  user: User;

  @ManyToOne(() => Course, (course) => course.enrollments, { onDelete: 'CASCADE' })
  course: Course;


  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date;
}
