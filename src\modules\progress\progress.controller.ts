import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  ParseIntPipe,
  UseGuards,
  Req,
} from '@nestjs/common';
import { ProgressService } from './progress.service';
import { CreateLessonContentProgressDto } from './dto/create-lesson-content-progress.dto';
import { UpdateLessonContentProgressDto } from './dto/update-lesson-content-progress.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';

@Controller('progress')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ProgressController {
  constructor(private readonly progressService: ProgressService) {}

  @Post('lesson-content')
  @Roles(UserRole.STUDENT, UserRole.INSTRUCTOR, UserRole.ADMIN)
  async createOrUpdateLessonContentProgress(
    @Body() createProgressDto: CreateLessonContentProgressDto,
    @Req() req: any
  ) {
    // Ensure user can only update their own progress (unless admin)
    if (req.user.role !== UserRole.ADMIN && createProgressDto.userId !== req.user.id) {
      createProgressDto.userId = req.user.id;
    }
    
    return this.progressService.createOrUpdateLessonContentProgress(createProgressDto);
  }

  @Get('course/:courseId/user/:userId')
  @Roles(UserRole.STUDENT, UserRole.INSTRUCTOR, UserRole.ADMIN)
  async getUserCourseProgress(
    @Param('courseId', ParseIntPipe) courseId: number,
    @Param('userId', ParseIntPipe) userId: number,
    @Req() req: any
  ) {
    // Ensure user can only view their own progress (unless admin/instructor)
    if (req.user.role === UserRole.STUDENT && userId !== req.user.id) {
      userId = req.user.id;
    }
    
    return this.progressService.getUserCourseProgress(userId, courseId);
  }

  @Get('program/:programId/user/:userId')
  @Roles(UserRole.STUDENT, UserRole.INSTRUCTOR, UserRole.ADMIN)
  async getUserProgramProgress(
    @Param('programId', ParseIntPipe) programId: number,
    @Param('userId', ParseIntPipe) userId: number,
    @Req() req: any
  ) {
    // Ensure user can only view their own progress (unless admin/instructor)
    if (req.user.role === UserRole.STUDENT && userId !== req.user.id) {
      userId = req.user.id;
    }
    
    return this.progressService.getUserProgramProgress(userId, programId);
  }

  @Get('my-course/:courseId')
  @Roles(UserRole.STUDENT, UserRole.INSTRUCTOR, UserRole.ADMIN)
  async getMyCourseProgress(
    @Param('courseId', ParseIntPipe) courseId: number,
    @Req() req: any
  ) {
    return this.progressService.getUserCourseProgress(req.user.id, courseId);
  }

  @Get('my-program/:programId')
  @Roles(UserRole.STUDENT, UserRole.INSTRUCTOR, UserRole.ADMIN)
  async getMyProgramProgress(
    @Param('programId', ParseIntPipe) programId: number,
    @Req() req: any
  ) {
    return this.progressService.getUserProgramProgress(req.user.id, programId);
  }
}
