import { Controller,Query,  Param, ParseIntPipe, UseGuards } from '@nestjs/common';
import { CoursesService } from './courses.service';
import { Get, Post, Put, Delete, Body } from '@nestjs/common';
import { Course } from './entities/course.entity';
import { CreateCourseDto } from './dto/cousres.dto';
import { UpdateCourseDto } from './dto/update-course.dto';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';
import { Public } from '../auth/decorators/public.decorator';
import { UploadFile  } from '../common/decorators/upload-file.decorator';
import { UploadedFile } from '@nestjs/common';
@Controller('courses')
export class CoursesController {
  constructor(private readonly coursesService: CoursesService) {}

// Public route - anyone can view courses
  @Public() 
  @Get('/')
  async getCourses(@Query('page') page?: string, @Query('limit') limit?: string) {
    const pageNumber = page && !isNaN(Number(page)) ? Number(page) : 1;
    const limitNumber = limit && !isNaN(Number(limit)) ? Number(limit) : 10;

    return this.coursesService.findAll(pageNumber, limitNumber);
  }


  // Public route - anyone can view a specific course
  @Public()
  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<Course | null> {
    return this.coursesService.findOne(id);
  }

  // Only instructors and admins can create courses
  @Post('/')
  @Roles(UserRole.INSTRUCTOR, UserRole.ADMIN)
  async create(@Body() createCourseDto: CreateCourseDto): Promise<Course> {
    return this.coursesService.create(createCourseDto);
  } 

  @Post(':id/upload-image')
  @UploadFile({ fieldName: 'file', destination: './uploads/courses/', fileType: 'image' })
  async uploadCourseImage(
    @Param('id', ParseIntPipe) id: number, 
    @UploadedFile() file: Express.Multer.File
  ) {
    return this.coursesService.updateThumbnail(id, file.filename);
  }

  // Only instructors and admins can update courses
  @Put(':id')
  @Roles(UserRole.INSTRUCTOR, UserRole.ADMIN)
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCourseDto: UpdateCourseDto,
  ): Promise<Course> {
    return this.coursesService.update(id, updateCourseDto);
  }

  // Only admins can delete courses
  @Delete(':id')
  @Roles(UserRole.ADMIN)
  async delete(@Param('id', ParseIntPipe) id: number): Promise<Course | null> {
    return this.coursesService.delete(id);
  }
}
