import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Program } from '../../programs/entities/program.entity';
import { ProgressStatus } from '../../common/enums/progress-status.enum';

@Entity('program_progress')
@Unique('IDX_program_progress_user_program', ['user', 'program'])
export class ProgramProgress {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'enum',
    enum: ProgressStatus,
    default: ProgressStatus.NOT_STARTED,
  })
  status: ProgressStatus;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0.0 })
  progressPercentage: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0.0, nullable: true })
  averageScore?: number;

  @Column({ type: 'int', default: 0 })
  completedCourses: number;

  @Column({ type: 'int', default: 0 })
  totalCourses: number;

  @Column({ type: 'int', default: 0 })
  timeSpentMinutes: number;

  @Column({ type: 'timestamptz', nullable: true })
  startedAt?: Date;

  @Column({ type: 'timestamptz', nullable: true })
  completedAt?: Date;

  /** RELATIONS **/
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  user: User;

  @ManyToOne(() => Program, { onDelete: 'CASCADE' })
  program: Program;

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date;
}
