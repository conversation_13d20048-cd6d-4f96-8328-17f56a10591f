import { IsEnum, IsNotEmpty, IsNumber, IsO<PERSON>al, IsString, Min, Max, IsObject } from 'class-validator';
import { ProgressStatus } from '../../common/enums/progress-status.enum';

export class CreateLessonContentProgressDto {
  @IsEnum(ProgressStatus)
  @IsOptional()
  status?: ProgressStatus;

  @IsNumber()
  @Min(0)
  @Max(100)
  @IsOptional()
  score?: number;

  @IsNumber()
  @Min(0)
  @IsOptional()
  timeSpentMinutes?: number;

  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;

  @IsString()
  @IsOptional()
  notes?: string;

  @IsNumber()
  @IsNotEmpty()
  userId: number;

  @IsNumber()
  @IsNotEmpty()
  lessonContentId: number;
}
