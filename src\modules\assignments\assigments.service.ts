import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Assignment } from './entities/assigment.entity';
import { CreateAssignmentDto } from './dto/create-assignment.dto';
import { UpdateAssigmentDto } from './dto/update-assigment.dto';
import { SubmitAssignmentDto, GradeAssignmentDto } from './dto/submit-assignment.dto';
import { LessonContent } from '../lesson-contents/entities/lesson-content.entity';
import { AssignmentSubmission, SubmissionStatus } from './entities/assignment-submission.entity';
import { User } from '../users/entities/user.entity';
import { ProgressService } from '../progress/progress.service';
import { ProgressStatus } from '../common/enums/progress-status.enum';

@Injectable()
@Injectable()
export class AssignmentService {
  constructor(
    @InjectRepository(Assignment)
    private readonly assignmentRepo: Repository<Assignment>,
    @InjectRepository(LessonContent)
    private readonly contentRepo: Repository<LessonContent>,
    @InjectRepository(AssignmentSubmission)
    private readonly submissionRepo: Repository<AssignmentSubmission>,
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,
    private readonly progressService: ProgressService,
  ) {}

   async create(dto: CreateAssignmentDto) {
        
    const content = await this.getLessonContentById(dto.lessonContentId);

     
    const { lessonContentId, ...data } = dto; 
    const assignment = this.assignmentRepo.create({ ...data, content });
    return this.assignmentRepo.save(assignment);
  }

  async getLessonContentById(id: number) {
    const content = await this.contentRepo.findOne({ where: { id } });
    if (!content) throw new NotFoundException(`LessonContent #${id} not found`);
    return content;
  }

 
  async findAll() {
    return this.assignmentRepo.find();
  }

  async findOne(id: number) {
    const assignment = await this.assignmentRepo.findOne({ where: { id }, relations: ['content.lesson'] });
    if (!assignment) throw new NotFoundException(`Assignment #${id} not found`);
    return assignment;
  }

  async update(id: number, data: UpdateAssigmentDto) {
    const assignment = await this.findOne(id);
    Object.assign(assignment, data);
    return this.assignmentRepo.save(assignment);
  }

  async remove(id: number) {
    const assignment = await this.findOne(id);
    return this.assignmentRepo.remove(assignment);
  }

  async submitAssignment(userId: number, submitDto: SubmitAssignmentDto) {
    const assignment = await this.findOne(submitDto.assignmentId);
    const user = await this.userRepo.findOne({ where: { id: userId } });

    if (!user) throw new NotFoundException('User not found');

    // Check if already submitted
    const existingSubmission = await this.submissionRepo.findOne({
      where: { student: { id: userId }, assignment: { id: submitDto.assignmentId } }
    });

    if (existingSubmission) {
      throw new BadRequestException('Assignment already submitted');
    }

    const submission = this.submissionRepo.create({
      submissionText: submitDto.submissionText,
      submissionFileUrl: submitDto.submissionFileUrl,
      timeSpentMinutes: submitDto.timeSpentMinutes,
      submittedAt: new Date(),
      student: user,
      assignment,
    });

    const savedSubmission = await this.submissionRepo.save(submission);

    // Update progress - assignment submitted but not graded yet
    await this.progressService.createOrUpdateLessonContentProgress({
      userId,
      lessonContentId: assignment.content.id,
      status: ProgressStatus.IN_PROGRESS,
      timeSpentMinutes: submitDto.timeSpentMinutes,
      metadata: {
        assignmentSubmission: {
          submissionId: savedSubmission.id,
          submittedAt: new Date(),
          status: SubmissionStatus.SUBMITTED,
        }
      }
    });

    return savedSubmission;
  }

  async gradeAssignment(instructorId: number, gradeDto: GradeAssignmentDto) {
    const submission = await this.submissionRepo.findOne({
      where: { id: gradeDto.submissionId },
      relations: ['assignment', 'assignment.content', 'student']
    });

    if (!submission) {
      throw new NotFoundException('Submission not found');
    }

    const instructor = await this.userRepo.findOne({ where: { id: instructorId } });
    if (!instructor) throw new NotFoundException('Instructor not found');

    submission.score = gradeDto.score;
    submission.feedback = gradeDto.feedback;
    submission.status = SubmissionStatus.GRADED;
    submission.gradedAt = new Date();
    submission.gradedBy = instructor;

    const gradedSubmission = await this.submissionRepo.save(submission);

    // Update progress with final score
    await this.progressService.createOrUpdateLessonContentProgress({
      userId: submission.student.id,
      lessonContentId: submission.assignment.content.id,
      status: ProgressStatus.COMPLETED,
      score: gradeDto.score,
      metadata: {
        assignmentSubmission: {
          submissionId: gradedSubmission.id,
          submittedAt: submission.submittedAt,
          gradedAt: gradedSubmission.gradedAt,
          status: SubmissionStatus.GRADED,
          score: gradeDto.score,
          feedback: gradeDto.feedback,
        }
      }
    });

    return gradedSubmission;
  }

  async getSubmissionsByAssignment(assignmentId: number) {
    return this.submissionRepo.find({
      where: { assignment: { id: assignmentId } },
      relations: ['student', 'gradedBy'],
      order: { submittedAt: 'DESC' }
    });
  }

  async getUserSubmissions(userId: number) {
    return this.submissionRepo.find({
      where: { student: { id: userId } },
      relations: ['assignment', 'assignment.content', 'gradedBy'],
      order: { submittedAt: 'DESC' }
    });
  }
}
