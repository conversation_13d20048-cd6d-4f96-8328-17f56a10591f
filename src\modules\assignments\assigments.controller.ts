import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
import { AssignmentService } from './assigments.service';
import { CreateAssignmentDto } from './dto/create-assignment.dto';
import { UpdateAssigmentDto } from './dto/update-assigment.dto';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';

@Controller('assignments')
export class AssignmentsController {
  constructor(private readonly assignmentsService: AssignmentService) {}

  // Only instructors and admins can create assignments
  @Post()
  @Roles(UserRole.INSTRUCTOR, UserRole.ADMIN)
  async create(@Body() createAssignmentDto: CreateAssignmentDto) {
    return this.assignmentsService.create(createAssignmentDto);
  }

  // Students, instructors, and admins can view assignments
  @Get()
  @Roles(UserRole.STUDENT, UserRole.INSTRUCTOR, UserRole.ADMIN)
  async findAll() {
    return this.assignmentsService.findAll();
  }

  // Students, instructors, and admins can view a specific assignment
  @Get(':id')
  @Roles(UserRole.STUDENT, UserRole.INSTRUCTOR, UserRole.ADMIN)
  async findOne(@Param('id') id: string) {
    return this.assignmentsService.findOne(+id);
  }

  // Only instructors and admins can update assignments
  @Patch(':id')
  @Roles(UserRole.INSTRUCTOR, UserRole.ADMIN)
  async update(@Param('id') id: string, @Body() updateAssignmentDto: UpdateAssigmentDto) {
    return this.assignmentsService.update(+id, updateAssignmentDto);
  }

  // Only admins can delete assignments
  @Delete(':id')
  @Roles(UserRole.ADMIN)
  async remove(@Param('id') id: string) {
    return this.assignmentsService.remove(+id);
  }
}
