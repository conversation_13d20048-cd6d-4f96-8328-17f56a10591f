import { Controller, Get, Post, Body, Patch, Param, Delete, ParseIntPipe, Req, UseGuards } from '@nestjs/common';
import { ProgramEnrollmentService } from './program-enrollment.service';
import { CreateProgramEnrollmentDto } from './dto/create-program-enrollment.dto';
import { UpdateProgramEnrollmentDto } from './dto/update-program-enrollment.dto';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';

@Controller('program-enrollments')
@UseGuards(JwtAuthGuard, RolesGuard)
export class ProgramEnrollmentController {
  constructor(private readonly service: ProgramEnrollmentService) {}

  @Post()
  @Roles(UserRole.STUDENT)
  create(@Body() dto: CreateProgramEnrollmentDto) {
    return this.service.create(dto);
  }

  @Get()
  @Roles(UserRole.ADMIN)
  findAll() {
    return this.service.findAll();
  }


    @Get('me')
  @Roles(UserRole.STUDENT)
  getMyEnrollments(@Req() req) {
    const userId = req.user.id; 
    return this.service.findByUser(userId);
  }

  @Get(':id')
  @Roles(UserRole.STUDENT, UserRole.ADMIN)
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.service.findOne(id);
  }
  
  @Patch(':id')
  @Roles(UserRole.STUDENT, UserRole.ADMIN)
  update(@Param('id', ParseIntPipe) id: number, @Body() dto: UpdateProgramEnrollmentDto) {
    return this.service.update(id, dto);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN)
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.service.remove(id);
  }
}
