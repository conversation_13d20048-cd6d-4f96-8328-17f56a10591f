import { MigrationInterface, QueryRunner, Table, TableForeignKey } from 'typeorm';

export class CreateLessonContentsTable1756200006000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(
            new Table({
                name: 'lesson_contents',
                columns: [
                    {
                        name: 'id',
                        type: 'int',
                        isPrimary: true,
                        isGenerated: true,
                        generationStrategy: 'increment',
                    },
                    {
                        name: 'type',
                        type: 'enum',
                        enum: ['video', 'audio', 'pdf', 'text', 'quiz', 'assignment'],
                        isNullable: false,
                    },
                    {
                        name: 'contentURL',
                        type: 'text',
                        isNullable: true,
                    },
                    {
                        name: 'text',
                        type: 'text',
                        isNullable: true,
                    },
                    {
                        name: 'createdAt',
                        type: 'timestamp',
                        default: 'NOW()',
                    },
                    {
                        name: 'lessonId',
                        type: 'int',
                        isNullable: true,
                    },
                ],
            }),
            true,
        );

        // Create foreign key for lesson
        await queryRunner.createForeignKey(
            'lesson_contents',
            new TableForeignKey({
                columnNames: ['lessonId'],
                referencedColumnNames: ['id'],
                referencedTableName: 'lessons',
                onDelete: 'CASCADE',
            }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const table = await queryRunner.getTable('lesson_contents');

        if (table) {
            // Drop foreign key
            const lessonForeignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf('lessonId') !== -1);

            if (lessonForeignKey) {
                await queryRunner.dropForeignKey('lesson_contents', lessonForeignKey);
            }
        }

        await queryRunner.dropTable('lesson_contents');
    }
}
