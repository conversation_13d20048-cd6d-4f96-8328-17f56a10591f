import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProgressService } from './progress.service';
import { ProgressController } from './progress.controller';
import { LessonContentProgress } from './entities/lesson-content-progress.entity';
import { LessonProgress } from './entities/lesson-progress.entity';
import { CourseProgress } from './entities/course-progress.entity';
import { ProgramProgress } from './entities/program-progress.entity';
import { LessonContent } from '../lesson-contents/entities/lesson-content.entity';
import { Lesson } from '../lessons/entities/lesson.entity';
import { Course } from '../courses/entities/course.entity';
import { Program } from '../programs/entities/program.entity';
import { User } from '../users/entities/user.entity';
import { CourseEnrollmentModule } from '../course-enrollment/course-enrollment.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      LessonContentProgress,
      LessonProgress,
      CourseProgress,
      ProgramProgress,
      LessonContent,
      Lesson,
      Course,
      Program,
      User,
    ]),
    CourseEnrollmentModule,
  ],
  controllers: [ProgressController],
  providers: [ProgressService],
  exports: [ProgressService],
})
export class ProgressModule {}
