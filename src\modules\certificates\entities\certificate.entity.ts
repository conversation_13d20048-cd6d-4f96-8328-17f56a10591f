import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Course } from '../../courses/entities/course.entity';
import { Program } from '../../programs/entities/program.entity';

export enum CertificateType {
  COURSE = 'course',
  PROGRAM = 'program',
}

@Entity('certificates')
@Unique('IDX_certificate_user_course', ['user', 'course'])
@Unique('IDX_certificate_user_program', ['user', 'program'])
export class Certificate {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 100, unique: true })
  certificateNumber: string; // Auto-generated unique certificate number

  @Column({
    type: 'enum',
    enum: CertificateType,
  })
  type: CertificateType;

  @Column({ type: 'varchar', length: 255 })
  title: string; // Certificate title

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  finalScore: number;

  @Column({ type: 'varchar', length: 10 })
  grade: string; // A+, A, B+, B, C+, C, D, F

  @Column({ type: 'timestamptz' })
  issuedAt: Date;

  @Column({ type: 'timestamptz' })
  completedAt: Date;

  @Column({ type: 'int' })
  totalTimeSpentMinutes: number;

  @Column({ type: 'json', nullable: true })
  metadata?: Record<string, any>; // Additional certificate data

  @Column({ type: 'boolean', default: true })
  isValid: boolean;

  /** RELATIONS **/
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  user: User;

  @ManyToOne(() => Course, { nullable: true, onDelete: 'CASCADE' })
  course?: Course;

  @ManyToOne(() => Program, { nullable: true, onDelete: 'CASCADE' })
  program?: Program;

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date;
}
