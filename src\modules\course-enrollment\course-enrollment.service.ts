import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CourseEnrollment } from './entities/course-enrollment.entity';
import { CreateCourseEnrollmentDto } from './dto/create-course-enrollment.dto';
import { UpdateCourseEnrollmentDto } from './dto/update-course-enrollment.dto';
import { User } from '../users/entities/user.entity';
import { Course } from '../courses/entities/course.entity';
import { EnrollmentStatus } from '../common/enums/progress-status.enum';

@Injectable()
export class CourseEnrollmentService {
  constructor(
    @InjectRepository(CourseEnrollment)
    private readonly enrollmentRepo: Repository<CourseEnrollment>,

    @InjectRepository(User)
    private readonly userRepo: Repository<User>,

    @InjectRepository(Course)
    private readonly courseRepo: Repository<Course>,
  ) {}

  /** CREATE new enrollment */
  async create(dto: CreateCourseEnrollmentDto): Promise<CourseEnrollment> {
    const user = await this.userRepo.findOneBy({ id: dto.userId });
    if (!user) throw new NotFoundException(`User ${dto.userId} not found`);

    const course = await this.courseRepo.findOneBy({ id: dto.courseId });
    if (!course) throw new NotFoundException(`Course ${dto.courseId} not found`);

    const enrollment = this.enrollmentRepo.create({
      ...dto,
      enrolledAt: new Date(dto.enrolledAt),
      user,
      course,
    });

    return this.enrollmentRepo.save(enrollment);
  }

  /** GET all enrollments */
  async findAll(): Promise<CourseEnrollment[]> {
    return this.enrollmentRepo.find({
      relations: ['user', 'course'],
    });
  }

  /** GET one enrollment by id */
  async findOne(id: number): Promise<CourseEnrollment> {
    const enrollment = await this.enrollmentRepo.findOne({
      where: { id },
      relations: ['user', 'course'],
    });
    if (!enrollment) {
      throw new NotFoundException(`Enrollment ${id} not found`);
    }
    return enrollment;
  }

  /** UPDATE enrollment */
  async update(id: number, dto: UpdateCourseEnrollmentDto): Promise<CourseEnrollment> {
    const enrollment = await this.findOne(id);

    Object.assign(enrollment, dto);

    if (dto.enrolledAt) {
      enrollment.enrolledAt = new Date(dto.enrolledAt);
    }

    return this.enrollmentRepo.save(enrollment);
  }

  /** DELETE enrollment */
  async remove(id: number): Promise<void> {
    const result = await this.enrollmentRepo.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Enrollment ${id} not found`);
    }
  }

  async isUserEnrolledInCourse(userId: number, courseId: number): Promise<boolean> {
    const enrollment = await this.enrollmentRepo.findOne({
      where: {
        user: { id: userId },
        course: { id: courseId },
        status: EnrollmentStatus.ACTIVE
      }
    });
    return !!enrollment;
  }

  async getUserEnrollments(userId: number): Promise<CourseEnrollment[]> {
    return this.enrollmentRepo.find({
      where: { user: { id: userId } },
      relations: ['course', 'course.instructor'],
      order: { enrolledAt: 'DESC' }
    });
  }

  async getCourseEnrollments(courseId: number): Promise<CourseEnrollment[]> {
    return this.enrollmentRepo.find({
      where: { course: { id: courseId } },
      relations: ['user'],
      order: { enrolledAt: 'DESC' }
    });
  }
}
