import { MigrationInterface, QueryRunner, TableColumn, TableForeignKey } from 'typeorm';

export class UpdateLessonContentsForOneToOne1756200010000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add foreign key columns for OneToOne relationships
        await queryRunner.addColumn('lesson_contents', new TableColumn({
            name: 'videoId',
            type: 'int',
            isNullable: true,
            isUnique: true,
        }));

        await queryRunner.addColumn('lesson_contents', new TableColumn({
            name: 'quizId',
            type: 'int',
            isNullable: true,
            isUnique: true,
        }));

        await queryRunner.addColumn('lesson_contents', new TableColumn({
            name: 'assignmentId',
            type: 'int',
            isNullable: true,
            isUnique: true,
        }));

        // Create foreign key constraints
        await queryRunner.createForeignKey('lesson_contents', new TableForeignKey({
            columnNames: ['videoId'],
            referencedColumnNames: ['id'],
            referencedTableName: 'videos',
            onDelete: 'SET NULL',
        }));

        await queryRunner.createForeignKey('lesson_contents', new TableForeignKey({
            columnNames: ['quizId'],
            referencedColumnNames: ['id'],
            referencedTableName: 'quizzes',
            onDelete: 'SET NULL',
        }));

        await queryRunner.createForeignKey('lesson_contents', new TableForeignKey({
            columnNames: ['assignmentId'],
            referencedColumnNames: ['id'],
            referencedTableName: 'assignments',
            onDelete: 'SET NULL',
        }));
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const table = await queryRunner.getTable('lesson_contents');
        
        if (table) {
            // Drop foreign keys
            const videoForeignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf('videoId') !== -1);
            const quizForeignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf('quizId') !== -1);
            const assignmentForeignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf('assignmentId') !== -1);
            
            if (videoForeignKey) {
                await queryRunner.dropForeignKey('lesson_contents', videoForeignKey);
            }
            if (quizForeignKey) {
                await queryRunner.dropForeignKey('lesson_contents', quizForeignKey);
            }
            if (assignmentForeignKey) {
                await queryRunner.dropForeignKey('lesson_contents', assignmentForeignKey);
            }
        }

        // Drop columns
        await queryRunner.dropColumn('lesson_contents', 'assignmentId');
        await queryRunner.dropColumn('lesson_contents', 'quizId');
        await queryRunner.dropColumn('lesson_contents', 'videoId');
    }
}
