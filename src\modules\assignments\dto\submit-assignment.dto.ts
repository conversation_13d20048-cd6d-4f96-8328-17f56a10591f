import { IsNotEmpty, <PERSON>N<PERSON>ber, IsString, IsOptional, IsUrl } from 'class-validator';

export class SubmitAssignmentDto {
  @IsNumber()
  @IsNotEmpty()
  assignmentId: number;

  @IsString()
  @IsOptional()
  submissionText?: string;

  @IsUrl()
  @IsOptional()
  submissionFileUrl?: string;

  @IsNumber()
  @IsNotEmpty()
  timeSpentMinutes: number;
}

export class GradeAssignmentDto {
  @IsNumber()
  @IsNotEmpty()
  submissionId: number;

  @IsNumber()
  @IsNotEmpty()
  score: number; // 0-100

  @IsString()
  @IsOptional()
  feedback?: string;
}
