import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CertificatesService } from './certificates.service';
import { CertificatesController } from './certificates.controller';
import { Certificate } from './entities/certificate.entity';
import { CourseProgress } from '../progress/entities/course-progress.entity';
import { ProgramProgress } from '../progress/entities/program-progress.entity';
import { User } from '../users/entities/user.entity';
import { Course } from '../courses/entities/course.entity';
import { Program } from '../programs/entities/program.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Certificate,
      CourseProgress,
      ProgramProgress,
      User,
      Course,
      Program,
    ])
  ],
  controllers: [CertificatesController],
  providers: [CertificatesService],
  exports: [CertificatesService],
})
export class CertificatesModule {}
