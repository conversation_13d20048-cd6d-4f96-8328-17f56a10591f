import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Certificate, CertificateType } from './entities/certificate.entity';
import { CourseProgress } from '../progress/entities/course-progress.entity';
import { ProgramProgress } from '../progress/entities/program-progress.entity';
import { User } from '../users/entities/user.entity';
import { Course } from '../courses/entities/course.entity';
import { Program } from '../programs/entities/program.entity';
import { ProgressStatus } from '../common/enums/progress-status.enum';

@Injectable()
export class CertificatesService {
  constructor(
    @InjectRepository(Certificate)
    private readonly certificateRepo: Repository<Certificate>,
    @InjectRepository(CourseProgress)
    private readonly courseProgressRepo: Repository<CourseProgress>,
    @InjectRepository(ProgramProgress)
    private readonly programProgressRepo: Repository<ProgramProgress>,
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,
    @InjectRepository(Course)
    private readonly courseRepo: Repository<Course>,
    @InjectRepository(Program)
    private readonly programRepo: Repository<Program>,
  ) {}

  private generateCertificateNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `CERT-${timestamp}-${random}`;
  }

  private calculateGrade(score: number): string {
    if (score >= 97) return 'A+';
    if (score >= 93) return 'A';
    if (score >= 90) return 'A-';
    if (score >= 87) return 'B+';
    if (score >= 83) return 'B';
    if (score >= 80) return 'B-';
    if (score >= 77) return 'C+';
    if (score >= 73) return 'C';
    if (score >= 70) return 'C-';
    if (score >= 67) return 'D+';
    if (score >= 65) return 'D';
    return 'F';
  }

  async generateCourseCertificate(userId: number, courseId: number): Promise<Certificate> {
    const user = await this.userRepo.findOne({ where: { id: userId } });
    if (!user) throw new NotFoundException('User not found');

    const course = await this.courseRepo.findOne({ 
      where: { id: courseId },
      relations: ['instructor']
    });
    if (!course) throw new NotFoundException('Course not found');

    const courseProgress = await this.courseProgressRepo.findOne({
      where: { user: { id: userId }, course: { id: courseId } }
    });

    if (!courseProgress || courseProgress.status !== ProgressStatus.COMPLETED) {
      throw new BadRequestException('Course must be completed to generate certificate');
    }

    // Check if certificate already exists
    const existingCertificate = await this.certificateRepo.findOne({
      where: { user: { id: userId }, course: { id: courseId } }
    });

    if (existingCertificate) {
      return existingCertificate;
    }

    const finalScore = courseProgress.averageScore || 0;
    const grade = this.calculateGrade(finalScore);

    const certificate = this.certificateRepo.create({
      certificateNumber: this.generateCertificateNumber(),
      type: CertificateType.COURSE,
      title: `Certificate of Completion - ${course.title}`,
      description: `This certifies that ${user.name} has successfully completed the course "${course.title}" with a final score of ${finalScore.toFixed(2)}%.`,
      finalScore,
      grade,
      issuedAt: new Date(),
      completedAt: courseProgress.completedAt || new Date(),
      totalTimeSpentMinutes: courseProgress.timeSpentMinutes,
      metadata: {
        courseName: course.title,
        instructorName: course.instructor?.name,
        completedLessons: courseProgress.completedLessons,
        totalLessons: courseProgress.totalLessons,
      },
      user,
      course,
    });

    return await this.certificateRepo.save(certificate);
  }

  async generateProgramCertificate(userId: number, programId: number): Promise<Certificate> {
    const user = await this.userRepo.findOne({ where: { id: userId } });
    if (!user) throw new NotFoundException('User not found');

    const program = await this.programRepo.findOne({ 
      where: { id: programId },
      relations: ['courses']
    });
    if (!program) throw new NotFoundException('Program not found');

    const programProgress = await this.programProgressRepo.findOne({
      where: { user: { id: userId }, program: { id: programId } }
    });

    if (!programProgress || programProgress.status !== ProgressStatus.COMPLETED) {
      throw new BadRequestException('Program must be completed to generate certificate');
    }

    // Check if certificate already exists
    const existingCertificate = await this.certificateRepo.findOne({
      where: { user: { id: userId }, program: { id: programId } }
    });

    if (existingCertificate) {
      return existingCertificate;
    }

    const finalScore = programProgress.averageScore || 0;
    const grade = this.calculateGrade(finalScore);

    const certificate = this.certificateRepo.create({
      certificateNumber: this.generateCertificateNumber(),
      type: CertificateType.PROGRAM,
      title: `Certificate of Completion - ${program.title}`,
      description: `This certifies that ${user.name} has successfully completed the program "${program.title}" with a final score of ${finalScore.toFixed(2)}%.`,
      finalScore,
      grade,
      issuedAt: new Date(),
      completedAt: programProgress.completedAt || new Date(),
      totalTimeSpentMinutes: programProgress.timeSpentMinutes,
      metadata: {
        programName: program.title,
        completedCourses: programProgress.completedCourses,
        totalCourses: programProgress.totalCourses,
        courseNames: program.courses.map(c => c.title),
      },
      user,
      program,
    });

    return await this.certificateRepo.save(certificate);
  }

  async getUserCertificates(userId: number): Promise<Certificate[]> {
    return await this.certificateRepo.find({
      where: { user: { id: userId } },
      relations: ['course', 'program'],
      order: { issuedAt: 'DESC' }
    });
  }

  async getCertificateByNumber(certificateNumber: string): Promise<Certificate> {
    const certificate = await this.certificateRepo.findOne({
      where: { certificateNumber },
      relations: ['user', 'course', 'program']
    });

    if (!certificate) {
      throw new NotFoundException('Certificate not found');
    }

    return certificate;
  }

  async verifyCertificate(certificateNumber: string): Promise<{ isValid: boolean; certificate?: Certificate }> {
    try {
      const certificate = await this.getCertificateByNumber(certificateNumber);
      return {
        isValid: certificate.isValid,
        certificate: certificate.isValid ? certificate : undefined
      };
    } catch (error) {
      return { isValid: false };
    }
  }

  async revokeCertificate(certificateId: number): Promise<Certificate> {
    const certificate = await this.certificateRepo.findOne({ where: { id: certificateId } });
    if (!certificate) {
      throw new NotFoundException('Certificate not found');
    }

    certificate.isValid = false;
    return await this.certificateRepo.save(certificate);
  }

  async getAllCertificates(page = 1, limit = 10): Promise<{
    data: Certificate[];
    total: number;
    page: number;
    limit: number;
  }> {
    const [data, total] = await this.certificateRepo.findAndCount({
      relations: ['user', 'course', 'program'],
      skip: (page - 1) * limit,
      take: limit,
      order: { issuedAt: 'DESC' }
    });

    return { data, total, page, limit };
  }
}
