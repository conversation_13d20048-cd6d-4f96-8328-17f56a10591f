import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateCertificatesTable1756200014000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(
            new Table({
                name: 'certificates',
                columns: [
                    {
                        name: 'id',
                        type: 'int',
                        isPrimary: true,
                        isGenerated: true,
                        generationStrategy: 'increment',
                    },
                    {
                        name: 'certificateNumber',
                        type: 'varchar',
                        length: '100',
                        isUnique: true,
                        isNullable: false,
                    },
                    {
                        name: 'type',
                        type: 'enum',
                        enum: ['course', 'program'],
                        isNullable: false,
                    },
                    {
                        name: 'title',
                        type: 'varchar',
                        length: '255',
                        isNullable: false,
                    },
                    {
                        name: 'description',
                        type: 'text',
                        isNullable: true,
                    },
                    {
                        name: 'finalScore',
                        type: 'decimal',
                        precision: 5,
                        scale: 2,
                        isNullable: false,
                    },
                    {
                        name: 'grade',
                        type: 'varchar',
                        length: '10',
                        isNullable: false,
                    },
                    {
                        name: 'issuedAt',
                        type: 'timestamp with time zone',
                        isNullable: false,
                    },
                    {
                        name: 'completedAt',
                        type: 'timestamp with time zone',
                        isNullable: false,
                    },
                    {
                        name: 'totalTimeSpentMinutes',
                        type: 'int',
                        isNullable: false,
                    },
                    {
                        name: 'metadata',
                        type: 'json',
                        isNullable: true,
                    },
                    {
                        name: 'isValid',
                        type: 'boolean',
                        default: true,
                    },
                    {
                        name: 'userId',
                        type: 'int',
                        isNullable: false,
                    },
                    {
                        name: 'courseId',
                        type: 'int',
                        isNullable: true,
                    },
                    {
                        name: 'programId',
                        type: 'int',
                        isNullable: true,
                    },
                    {
                        name: 'createdAt',
                        type: 'timestamp with time zone',
                        default: 'NOW()',
                    },
                    {
                        name: 'updatedAt',
                        type: 'timestamp with time zone',
                        default: 'NOW()',
                    },
                ],
                foreignKeys: [
                    {
                        columnNames: ['userId'],
                        referencedTableName: 'users',
                        referencedColumnNames: ['id'],
                        onDelete: 'CASCADE',
                    },
                    {
                        columnNames: ['courseId'],
                        referencedTableName: 'courses',
                        referencedColumnNames: ['id'],
                        onDelete: 'CASCADE',
                    },
                    {
                        columnNames: ['programId'],
                        referencedTableName: 'programs',
                        referencedColumnNames: ['id'],
                        onDelete: 'CASCADE',
                    },
                ],
                indices: [
                    {
                        name: 'IDX_certificate_user_course',
                        columnNames: ['userId', 'courseId'],
                        isUnique: true,
                        where: 'courseId IS NOT NULL',
                    },
                    {
                        name: 'IDX_certificate_user_program',
                        columnNames: ['userId', 'programId'],
                        isUnique: true,
                        where: 'programId IS NOT NULL',
                    },
                ],
            }),
            true,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable('certificates');
    }
}
