import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LessonContentProgress } from './entities/lesson-content-progress.entity';
import { LessonProgress } from './entities/lesson-progress.entity';
import { CourseProgress } from './entities/course-progress.entity';
import { ProgramProgress } from './entities/program-progress.entity';
import { CreateLessonContentProgressDto } from './dto/create-lesson-content-progress.dto';
import { UpdateLessonContentProgressDto } from './dto/update-lesson-content-progress.dto';
import { ProgressStatus } from '../common/enums/progress-status.enum';
import { LessonContent } from '../lesson-contents/entities/lesson-content.entity';
import { Lesson } from '../lessons/entities/lesson.entity';
import { Course } from '../courses/entities/course.entity';
import { Program } from '../programs/entities/program.entity';
import { User } from '../users/entities/user.entity';
import { CourseEnrollmentService } from '../course-enrollment/course-enrollment.service';

@Injectable()
export class ProgressService {
  constructor(
    @InjectRepository(LessonContentProgress)
    private readonly lessonContentProgressRepo: Repository<LessonContentProgress>,
    @InjectRepository(LessonProgress)
    private readonly lessonProgressRepo: Repository<LessonProgress>,
    @InjectRepository(CourseProgress)
    private readonly courseProgressRepo: Repository<CourseProgress>,
    @InjectRepository(ProgramProgress)
    private readonly programProgressRepo: Repository<ProgramProgress>,
    @InjectRepository(LessonContent)
    private readonly lessonContentRepo: Repository<LessonContent>,
    @InjectRepository(Lesson)
    private readonly lessonRepo: Repository<Lesson>,
    @InjectRepository(Course)
    private readonly courseRepo: Repository<Course>,
    @InjectRepository(Program)
    private readonly programRepo: Repository<Program>,
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,
    private readonly courseEnrollmentService: CourseEnrollmentService,
  ) {}

  // Lesson Content Progress
  async createOrUpdateLessonContentProgress(dto: CreateLessonContentProgressDto) {
    const user = await this.userRepo.findOne({ where: { id: dto.userId } });
    if (!user) throw new NotFoundException('User not found');

    const lessonContent = await this.lessonContentRepo.findOne({ 
      where: { id: dto.lessonContentId },
      relations: ['lesson', 'lesson.course', 'lesson.course.program']
    });
    if (!lessonContent) throw new NotFoundException('Lesson content not found');

    // Check if user is enrolled in the course
    const isEnrolled = await this.courseEnrollmentService.isUserEnrolledInCourse(
      dto.userId,
      lessonContent.lesson.course.id
    );
    if (!isEnrolled) {
      throw new BadRequestException('User must be enrolled in the course to track progress');
    }

    let progress = await this.lessonContentProgressRepo.findOne({
      where: { user: { id: dto.userId }, lessonContent: { id: dto.lessonContentId } }
    });

    if (!progress) {
      progress = this.lessonContentProgressRepo.create({
        user,
        lessonContent,
        status: dto.status || ProgressStatus.NOT_STARTED,
        score: dto.score,
        timeSpentMinutes: dto.timeSpentMinutes || 0,
        metadata: dto.metadata,
        notes: dto.notes,
        startedAt: dto.status === ProgressStatus.IN_PROGRESS ? new Date() : undefined,
        completedAt: dto.status === ProgressStatus.COMPLETED ? new Date() : undefined,
      });
    } else {
      progress.status = dto.status || progress.status;
      progress.score = dto.score !== undefined ? dto.score : progress.score;
      progress.timeSpentMinutes = (progress.timeSpentMinutes || 0) + (dto.timeSpentMinutes || 0);
      progress.metadata = dto.metadata || progress.metadata;
      progress.notes = dto.notes || progress.notes;
      
      if (dto.status === ProgressStatus.IN_PROGRESS && !progress.startedAt) {
        progress.startedAt = new Date();
      }
      if (dto.status === ProgressStatus.COMPLETED && !progress.completedAt) {
        progress.completedAt = new Date();
      }
    }

    const savedProgress = await this.lessonContentProgressRepo.save(progress);

    // Update lesson progress
    await this.updateLessonProgress(dto.userId, lessonContent.lesson.id);

    return savedProgress;
  }

  async updateLessonProgress(userId: number, lessonId: number) {
    const lesson = await this.lessonRepo.findOne({
      where: { id: lessonId },
      relations: ['contents', 'course']
    });
    if (!lesson) throw new NotFoundException('Lesson not found');

    const contentProgresses = await this.lessonContentProgressRepo.find({
      where: { user: { id: userId }, lessonContent: { lesson: { id: lessonId } } }
    });

    const totalContents = lesson.contents.length;
    const completedContents = contentProgresses.filter(p => p.status === ProgressStatus.COMPLETED).length;
    const progressPercentage = totalContents > 0 ? (completedContents / totalContents) * 100 : 0;
    
    const averageScore = contentProgresses.length > 0 
      ? contentProgresses.reduce((sum, p) => sum + (p.score || 0), 0) / contentProgresses.length 
      : 0;

    const totalTimeSpent = contentProgresses.reduce((sum, p) => sum + (p.timeSpentMinutes || 0), 0);

    let lessonProgress = await this.lessonProgressRepo.findOne({
      where: { user: { id: userId }, lesson: { id: lessonId } }
    });

    const status = progressPercentage === 100 ? ProgressStatus.COMPLETED 
      : progressPercentage > 0 ? ProgressStatus.IN_PROGRESS 
      : ProgressStatus.NOT_STARTED;

    if (!lessonProgress) {
      lessonProgress = this.lessonProgressRepo.create({
        user: { id: userId },
        lesson: { id: lessonId },
        status,
        progressPercentage,
        averageScore,
        completedContents,
        totalContents,
        timeSpentMinutes: totalTimeSpent,
        startedAt: status !== ProgressStatus.NOT_STARTED ? new Date() : undefined,
        completedAt: status === ProgressStatus.COMPLETED ? new Date() : undefined,
      });
    } else {
      lessonProgress.status = status;
      lessonProgress.progressPercentage = progressPercentage;
      lessonProgress.averageScore = averageScore;
      lessonProgress.completedContents = completedContents;
      lessonProgress.totalContents = totalContents;
      lessonProgress.timeSpentMinutes = totalTimeSpent;
      
      if (status !== ProgressStatus.NOT_STARTED && !lessonProgress.startedAt) {
        lessonProgress.startedAt = new Date();
      }
      if (status === ProgressStatus.COMPLETED && !lessonProgress.completedAt) {
        lessonProgress.completedAt = new Date();
      }
    }

    const savedLessonProgress = await this.lessonProgressRepo.save(lessonProgress);

    // Update course progress
    await this.updateCourseProgress(userId, lesson.course.id);

    return savedLessonProgress;
  }

  async updateCourseProgress(userId: number, courseId: number) {
    const course = await this.courseRepo.findOne({
      where: { id: courseId },
      relations: ['lessons', 'program']
    });
    if (!course) throw new NotFoundException('Course not found');

    const lessonProgresses = await this.lessonProgressRepo.find({
      where: { user: { id: userId }, lesson: { course: { id: courseId } } }
    });

    const totalLessons = course.lessons.length;
    const completedLessons = lessonProgresses.filter(p => p.status === ProgressStatus.COMPLETED).length;
    const progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0;
    
    const averageScore = lessonProgresses.length > 0 
      ? lessonProgresses.reduce((sum, p) => sum + (p.averageScore || 0), 0) / lessonProgresses.length 
      : 0;

    const totalTimeSpent = lessonProgresses.reduce((sum, p) => sum + (p.timeSpentMinutes || 0), 0);

    let courseProgress = await this.courseProgressRepo.findOne({
      where: { user: { id: userId }, course: { id: courseId } }
    });

    const status = progressPercentage === 100 ? ProgressStatus.COMPLETED 
      : progressPercentage > 0 ? ProgressStatus.IN_PROGRESS 
      : ProgressStatus.NOT_STARTED;

    if (!courseProgress) {
      courseProgress = this.courseProgressRepo.create({
        user: { id: userId },
        course: { id: courseId },
        status,
        progressPercentage,
        averageScore,
        completedLessons,
        totalLessons,
        timeSpentMinutes: totalTimeSpent,
        startedAt: status !== ProgressStatus.NOT_STARTED ? new Date() : undefined,
        completedAt: status === ProgressStatus.COMPLETED ? new Date() : undefined,
      });
    } else {
      courseProgress.status = status;
      courseProgress.progressPercentage = progressPercentage;
      courseProgress.averageScore = averageScore;
      courseProgress.completedLessons = completedLessons;
      courseProgress.totalLessons = totalLessons;
      courseProgress.timeSpentMinutes = totalTimeSpent;
      
      if (status !== ProgressStatus.NOT_STARTED && !courseProgress.startedAt) {
        courseProgress.startedAt = new Date();
      }
      if (status === ProgressStatus.COMPLETED && !courseProgress.completedAt) {
        courseProgress.completedAt = new Date();
      }
    }

    const savedCourseProgress = await this.courseProgressRepo.save(courseProgress);

    // Update program progress if course belongs to a program
    if (course.program) {
      await this.updateProgramProgress(userId, course.program.id);
    }

    return savedCourseProgress;
  }

  async updateProgramProgress(userId: number, programId: number) {
    const program = await this.programRepo.findOne({
      where: { id: programId },
      relations: ['courses']
    });
    if (!program) throw new NotFoundException('Program not found');

    const courseProgresses = await this.courseProgressRepo.find({
      where: { user: { id: userId }, course: { program: { id: programId } } }
    });

    const totalCourses = program.courses.length;
    const completedCourses = courseProgresses.filter(p => p.status === ProgressStatus.COMPLETED).length;
    const progressPercentage = totalCourses > 0 ? (completedCourses / totalCourses) * 100 : 0;
    
    const averageScore = courseProgresses.length > 0 
      ? courseProgresses.reduce((sum, p) => sum + (p.averageScore || 0), 0) / courseProgresses.length 
      : 0;

    const totalTimeSpent = courseProgresses.reduce((sum, p) => sum + (p.timeSpentMinutes || 0), 0);

    let programProgress = await this.programProgressRepo.findOne({
      where: { user: { id: userId }, program: { id: programId } }
    });

    const status = progressPercentage === 100 ? ProgressStatus.COMPLETED 
      : progressPercentage > 0 ? ProgressStatus.IN_PROGRESS 
      : ProgressStatus.NOT_STARTED;

    if (!programProgress) {
      programProgress = this.programProgressRepo.create({
        user: { id: userId },
        program: { id: programId },
        status,
        progressPercentage,
        averageScore,
        completedCourses,
        totalCourses,
        timeSpentMinutes: totalTimeSpent,
        startedAt: status !== ProgressStatus.NOT_STARTED ? new Date() : undefined,
        completedAt: status === ProgressStatus.COMPLETED ? new Date() : undefined,
      });
    } else {
      programProgress.status = status;
      programProgress.progressPercentage = progressPercentage;
      programProgress.averageScore = averageScore;
      programProgress.completedCourses = completedCourses;
      programProgress.totalCourses = totalCourses;
      programProgress.timeSpentMinutes = totalTimeSpent;
      
      if (status !== ProgressStatus.NOT_STARTED && !programProgress.startedAt) {
        programProgress.startedAt = new Date();
      }
      if (status === ProgressStatus.COMPLETED && !programProgress.completedAt) {
        programProgress.completedAt = new Date();
      }
    }

    return await this.programProgressRepo.save(programProgress);
  }

  // Get user progress for a course
  async getUserCourseProgress(userId: number, courseId: number) {
    const courseProgress = await this.courseProgressRepo.findOne({
      where: { user: { id: userId }, course: { id: courseId } },
      relations: ['user', 'course']
    });

    const lessonProgresses = await this.lessonProgressRepo.find({
      where: { user: { id: userId }, lesson: { course: { id: courseId } } },
      relations: ['lesson']
    });

    return {
      courseProgress,
      lessonProgresses,
    };
  }

  // Get user progress for a program
  async getUserProgramProgress(userId: number, programId: number) {
    const programProgress = await this.programProgressRepo.findOne({
      where: { user: { id: userId }, program: { id: programId } },
      relations: ['user', 'program']
    });

    const courseProgresses = await this.courseProgressRepo.find({
      where: { user: { id: userId }, course: { program: { id: programId } } },
      relations: ['course']
    });

    return {
      programProgress,
      courseProgresses,
    };
  }
}
