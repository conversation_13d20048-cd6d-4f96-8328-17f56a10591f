import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { LessonContent } from '../../lesson-contents/entities/lesson-content.entity';
import { ProgressStatus } from '../../common/enums/progress-status.enum';

@Entity('lesson_content_progress')
@Unique('IDX_lesson_content_progress_user_content', ['user', 'lessonContent'])
export class LessonContentProgress {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'enum',
    enum: ProgressStatus,
    default: ProgressStatus.NOT_STARTED,
  })
  status: ProgressStatus;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0.0, nullable: true })
  score?: number;

  @Column({ type: 'int', default: 0 })
  timeSpentMinutes: number;

  @Column({ type: 'timestamptz', nullable: true })
  startedAt?: Date;

  @Column({ type: 'timestamptz', nullable: true })
  completedAt?: Date;

  @Column({ type: 'json', nullable: true })
  metadata?: Record<string, any>; // For storing quiz answers, assignment submissions, etc.

  @Column({ type: 'text', nullable: true })
  notes?: string;

  /** RELATIONS **/
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  user: User;

  @ManyToOne(() => LessonContent, { onDelete: 'CASCADE' })
  lessonContent: LessonContent;

  @CreateDateColumn({ type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamptz' })
  updatedAt: Date;
}
