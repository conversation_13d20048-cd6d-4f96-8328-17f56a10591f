import {
  Controller,
  Get,
  Post,
  Param,
  ParseIntPipe,
  UseGuards,
  Req,
  Query,
  Patch,
} from '@nestjs/common';
import { CertificatesService } from './certificates.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';
import { Public } from '../auth/decorators/public.decorator';

@Controller('certificates')
@UseGuards(JwtAuthGuard, RolesGuard)
export class CertificatesController {
  constructor(private readonly certificatesService: CertificatesService) {}

  @Post('course/:courseId')
  @Roles(UserRole.STUDENT, UserRole.INSTRUCTOR, UserRole.ADMIN)
  async generateCourseCertificate(
    @Param('courseId', ParseIntPipe) courseId: number,
    @Req() req: any
  ) {
    return this.certificatesService.generateCourseCertificate(req.user.id, courseId);
  }

  @Post('program/:programId')
  @Roles(UserRole.STUDENT, UserRole.INSTRUCTOR, UserRole.ADMIN)
  async generateProgramCertificate(
    @Param('programId', ParseIntPipe) programId: number,
    @Req() req: any
  ) {
    return this.certificatesService.generateProgramCertificate(req.user.id, programId);
  }

  @Get('my-certificates')
  @Roles(UserRole.STUDENT, UserRole.INSTRUCTOR, UserRole.ADMIN)
  async getMyCertificates(@Req() req: any) {
    return this.certificatesService.getUserCertificates(req.user.id);
  }

  @Get('user/:userId')
  @Roles(UserRole.INSTRUCTOR, UserRole.ADMIN)
  async getUserCertificates(@Param('userId', ParseIntPipe) userId: number) {
    return this.certificatesService.getUserCertificates(userId);
  }

  @Public()
  @Get('verify/:certificateNumber')
  async verifyCertificate(@Param('certificateNumber') certificateNumber: string) {
    return this.certificatesService.verifyCertificate(certificateNumber);
  }

  @Public()
  @Get('view/:certificateNumber')
  async getCertificateByNumber(@Param('certificateNumber') certificateNumber: string) {
    return this.certificatesService.getCertificateByNumber(certificateNumber);
  }

  @Get()
  @Roles(UserRole.ADMIN)
  async getAllCertificates(
    @Query('page') page?: string,
    @Query('limit') limit?: string
  ) {
    const pageNumber = page && !isNaN(Number(page)) ? Number(page) : 1;
    const limitNumber = limit && !isNaN(Number(limit)) ? Number(limit) : 10;
    
    return this.certificatesService.getAllCertificates(pageNumber, limitNumber);
  }

  @Patch(':id/revoke')
  @Roles(UserRole.ADMIN)
  async revokeCertificate(@Param('id', ParseIntPipe) id: number) {
    return this.certificatesService.revokeCertificate(id);
  }
}
